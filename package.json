{"name": "carbonfootprint", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@playwright/test": "^1.54.2", "@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "eslint": "9.32.0", "eslint-config-next": "15.4.5", "postcss": "^8.5.6", "typescript": "^5.9.2"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "next": "^15.4.5", "react": "^19.1.1", "react-dom": "^19.1.1", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17"}}